% VERSION 2
% WARNING: this file is auto-generated; do not edit
% UNSUPPORTED: this file and its format may change and/or
%   may be removed in a future release
! access-bridge-64.jar
com/sun/java/accessibility/
! cldrdata.jar
sun/text
sun/util
# dnsns.jar
META-INF/services/sun.net.spi.nameservice.NameServiceDescriptor
sun/net
! jaccess.jar
com/sun/java/accessibility/
# localedata.jar
sun/text
sun/util
# nashorn.jar
jdk/nashorn
META-INF/services/javax.script.ScriptEngineFactory
jdk/internal
# sunec.jar
sun/security
META-INF/ORACLE_J.RSA
META-INF/ORACLE_J.SF
# sunjce_provider.jar
com/sun/crypto/
META-INF/ORACLE_J.RSA
META-INF/ORACLE_J.SF
# sunmscapi.jar
sun/security
META-INF/ORACLE_J.RSA
META-INF/ORACLE_J.SF
# sunpkcs11.jar
sun/security
META-INF/ORACLE_J.RSA
META-INF/ORACLE_J.SF
# zipfs.jar
META-INF/services/java.nio.file.spi.FileSystemProvider
com/sun/nio/
# jfxrt.jar
META-INF/INDEX.LIST
com/sun/deploy/uitoolkit/impl/fx/
com/sun/glass/events/
com/sun/glass/ui/
com/sun/glass/utils/
com/sun/javafx/
com/sun/media/jfxmedia/
com/sun/media/jfxmediaimpl/
com/sun/openpisces/
com/sun/pisces/
com/sun/prism/
com/sun/scenario/
com/sun/webkit/
javafx/animation/
javafx/application/
javafx/beans/
javafx/collections/
javafx/concurrent/
javafx/css/
javafx/embed/swing/
javafx/event/
javafx/fxml/
javafx/geometry/
javafx/print/
javafx/scene/
javafx/stage/
javafx/util/
netscape/javascript/
