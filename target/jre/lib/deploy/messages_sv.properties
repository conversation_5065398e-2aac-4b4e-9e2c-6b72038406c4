#
# Copyright (c) 2004, 2018, Oracle and/or its affiliates. All rights reserved.
# ORACLE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
#

error.internal.badmsg=internt fel, ok\u00E4nt meddelande
error.badinst.nojre=Felaktig installation. Ingen JRE har hittats i konfigurationsfilen
error.launch.execv=Ett fel intr\u00E4ffade under starten av Java Web Start (execv)
error.launch.sysexec=Ett fel intr\u00E4ffade under starten av Java Web Start (SysExec) 
error.listener.failed=V\u00E4lkomstsk\u00E4rm: sysCreateListenerSocket utf\u00F6rdes inte
error.accept.failed=V\u00E4lkomstsk\u00E4rm: kunde inte accepteras
error.recv.failed=V\u00E4lkomstsk\u00E4rm: kunde inte mottaga
error.invalid.port=V\u00E4lkomstsk\u00E4rm: \u00E5terskapade inte en giltig port
error.read=L\u00E4ste f\u00F6rbi slutet av bufferten
error.xmlparsing=XML-tolkningsfel: fel typ av token hittades
error.splash.exit=Java Web Start - v\u00E4lkomstsk\u00E4rmen avslutas .....\n
# "Last WinSock Error" means the error message for the last operation that failed.
error.winsock=\tSenaste WinSock-fel: 
error.winsock.load=Kunde inte ladda winsock.dll
error.winsock.start=WSAStartup utf\u00F6rdes inte
error.badinst.nohome=Felaktig installation: JAVAWS_HOME har inte st\u00E4llts in 
error.splash.noimage=V\u00E4lkomstsk\u00E4rm: kunde inte ladda bilden f\u00F6r v\u00E4lkomstsk\u00E4rmen
error.splash.socket=V\u00E4lkomstsk\u00E4rm: serversocket utf\u00F6rdes inte
error.splash.cmnd=V\u00E4lkomstsk\u00E4rm: ok\u00E4nt kommando
error.splash.port=V\u00E4lkomstsk\u00E4rm: porten angavs inte
error.splash.send=V\u00E4lkomstsk\u00E4rm: kunde inte skicka
error.splash.timer=V\u00E4lkomstsk\u00E4rm: kunde inte skapa tidtagare f\u00F6r avst\u00E4ngning
error.splash.x11.open=V\u00E4lkomstsk\u00E4rm: kan inte \u00F6ppna X11-visningen
error.splash.x11.connect=V\u00E4lkomstsk\u00E4rm: X11-anslutning uppr\u00E4ttades inte
# Javaws usage: '\' is a joining of two sentence,which are connected including
# the invisible character '\n'.
message.javaws.usage=\nSyntax:\tjavaws [k\u00F6ralternativ] <jnlp-fil>\t\n\tjavaws [k\u00F6ralternativ]\t\t\n\nd\u00E4r k\u00F6ralternativen omfattar:\t\t\t\n-verbose       \tvisa ytterligare utdata\t\n-offline       \tk\u00F6r applikationen i offlinel\u00E4ge\t\n-system        \tk\u00F6r applikationen endast fr\u00E5n systemcachen\n-Xnosplash     \tk\u00F6r utan att visa v\u00E4lkomstsk\u00E4rmen\t\n-J<alternativ>     \tange alternativ f\u00F6r VM\t\n-wait          \tstarta javaprocessen och v\u00E4nta tills den har slutf\u00F6rts\t\n\nkontrollalternativen omfattar:\t\n-viewer        \tvisa cachel\u00E4saren i kontrollpanelen f\u00F6r java\n-clearcache    \tta bort alla icke installerade applikationer fr\u00E5n cachen\n-uninstall     \tta bort alla applikationer fr\u00E5n cachen\n-uninstall <jnlp-fil>              \tta bort applikationen fr\u00E5n cachen\t\n-import [importalternativ] <jnlp-fil>\timportera applikationen till cachen\t\t\n\nimportalternativen omfattar:\t\t\t\t\t\t\n-silent        \timportera obevakat (utan anv\u00E4ndargr\u00E4nssnitt)\t\n-system        \timportera applikationen till systemcachen\t\n-codebase <url>\th\u00E4mta resurserna fr\u00E5n den angivna kodbasen\t\n-shortcut      \tinstallera genv\u00E4gar som om anv\u00E4ndaren till\u00E5tit det\t\n-association   \tinstallera associationer som om anv\u00E4ndaren till\u00E5tit det\t\n\n
