<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.kalle</groupId>
    <artifactId>go-game</artifactId>
    <version>1.0.1</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-all</artifactId>
            <version>1.16</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <mainClass>com.kalle.gogame.GoGame</mainClass>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.akathist.maven.plugins.launch4j</groupId>
                <artifactId>launch4j-maven-plugin</artifactId>
                <version>2.1.2</version>
                <executions>
                    <execution>
                        <id>l4j-clui</id>
                        <phase>package</phase>
                        <goals>
                            <goal>launch4j</goal>
                        </goals>
                        <configuration>
                            <headerType>gui</headerType>
                            <jar>${project.build.directory}/${project.artifactId}-${project.version}.jar</jar>
                            <outfile>${project.build.directory}/GoGame.exe</outfile>
                            <errTitle>GoGame</errTitle>
                            <cmdLine></cmdLine>
                            <chdir>.</chdir>
                            <priority>normal</priority>
                            <downloadUrl>http://java.com/download</downloadUrl>
                            <supportUrl></supportUrl>
                            <stayAlive>false</stayAlive>
                            <restartOnCrash>false</restartOnCrash>
                            <manifest></manifest>
                            <icon></icon>
                            <jre>
                                <path>./jre</path>
                                <minVersion>1.8.0</minVersion>
                                <initialHeapSize>128</initialHeapSize>
                                <maxHeapSize>1024</maxHeapSize>
                            </jre>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>