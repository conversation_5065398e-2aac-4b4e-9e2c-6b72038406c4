package com.kalle.gogame;

import javafx.scene.paint.Color;

import java.util.Set;

/**
 * 行棋记录
 *
 * <AUTHOR>
 * @since 2022-11-18
 */
public class GoRecord {

    /**
     * 颜色
     */
    private final Color color;

    /**
     * 落子的棋子
     */
    private final GoItem killer;

    /**
     * 被杀的棋子集合
     */
    private final Set<GoItem> killGoItems;

    public GoRecord(Color color, GoItem killer, Set<GoItem> killGoItems) {
        this.color = color;
        this.killer = killer;
        this.killGoItems = killGoItems;
    }

    public Color getColor() {
        return color;
    }

    public GoItem getKiller() {
        return killer;
    }

    public Set<GoItem> getKillGoItems() {
        return killGoItems;
    }
}
