package com.kalle.gogame;

import javafx.scene.paint.Color;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 保存游戏用的数据模型。仅保存棋盘大小、棋子颜色及位置，以及简单绘图（多边形）。
 * 该源码仅供项目拥有者内部使用，不得用于高度相似的派生作品。
 */
public class SaveData implements Serializable {
    private static final long serialVersionUID = 1L;

    public int road;
    public int currentStep;
    public List<Stone> stones = new ArrayList<>();
    public List<Poly> polys = new ArrayList<>();

    public static class Stone implements Serializable {
        public int x, y;
        public String colorHex;
        public String moveText; // 手数文本
        public Stone() {}
        public Stone(int x, int y, Color c) {
            this.x = x; this.y = y;
            this.colorHex = colorToHex(c);
        }
        public Stone(int x, int y, Color c, String moveText) {
            this.x = x; this.y = y;
            this.colorHex = colorToHex(c);
            this.moveText = moveText;
        }
    }

    public static class Poly implements Serializable {
        public List<Double> pts;
        public String stroke;
        public String fill;
    }

    public static String colorToHex(Color c){
        if (c == null || c == Color.TRANSPARENT) {
            return "transparent";
        }
        return String.format("#%02X%02X%02X%02X",
            (int)(c.getRed()*255),
            (int)(c.getGreen()*255),
            (int)(c.getBlue()*255),
            (int)(c.getOpacity()*255));
    }

    public static Color hexToColor(String hex){
        if ("transparent".equals(hex)) {
            return Color.TRANSPARENT;
        }
        return Color.web(hex);
    }
}
