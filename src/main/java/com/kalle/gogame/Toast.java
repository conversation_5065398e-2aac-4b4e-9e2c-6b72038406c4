package com.kalle.gogame;

import javafx.scene.Scene;
import javafx.scene.control.Alert;
import javafx.scene.control.Label;
import javafx.scene.layout.StackPane;
import javafx.stage.Stage;
import javafx.stage.StageStyle;
import javafx.util.Duration;
import javafx.animation.PauseTransition;
import javafx.application.Platform;

/**
 * 消息提示工具类
 *
 * <AUTHOR>
 * @since 2022-11-18
 */
public class Toast {

    public static void toast(String message) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle("提示");
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        });
    }

    public static void toast(Stage owner, String message) {
        Platform.runLater(() -> {
            Stage toastStage = new Stage();
            toastStage.initOwner(owner);
            toastStage.setResizable(false);
            toastStage.initStyle(StageStyle.TRANSPARENT);

            Label label = new Label(message);
            label.setStyle(
                    "-fx-background-color: rgba(0, 0, 0, 0.7); " +
                            "-fx-text-fill: white; " +
                            "-fx-padding: 10px; " +
                            "-fx-border-radius: 5px; " +
                            "-fx-background-radius: 5px;"
            );

            StackPane root = new StackPane(label);
            root.setStyle("-fx-background-color: transparent;");

            Scene scene = new Scene(root);
            scene.setFill(null);
            toastStage.setScene(scene);

            toastStage.setX(owner.getX() + owner.getWidth() / 2 - 75);
            toastStage.setY(owner.getY() + owner.getHeight() / 2 - 25);

            toastStage.show();

            PauseTransition delay = new PauseTransition(Duration.seconds(1.5));
            delay.setOnFinished(event -> toastStage.close());
            delay.play();
        });
    }
}
