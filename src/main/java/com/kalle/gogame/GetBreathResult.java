package com.kalle.gogame;

import javafx.scene.paint.Color;

import java.util.List;
import java.util.Set;

/**
 * 获取气数的结果
 *
 * <AUTHOR>
 * @since 2022-11-18
 */
public class GetBreathResult {

    /**
     * 气数
     */
    private final int breaths;

    /**
     * 相关棋子集合
     */
    private final List<GoItem> groupItems;

    public GetBreathResult(int breaths, List<GoItem> groupItems) {
        this.breaths = breaths;
        this.groupItems = groupItems;
    }

    public int getBreaths() {
        return breaths;
    }

    public List<GoItem> getGroupItems() {
        return groupItems;
    }
}
