package com.kalle.gogame;

import javafx.scene.paint.Color;

/**
 * 围棋棋子
 *
 * <AUTHOR>
 * @since 2022-11-18
 */
public class GoItem extends javafx.scene.shape.Circle {

    /**
     * x坐标，从0开始
     */
    private final int x;

    /**
     * y坐标，从0开始
     */
    private final int y;

    /**
     * 棋子颜色
     */
    private Color color;

    /**
     * 上
     */
    private GoItem up;

    /**
     * 下
     */
    private GoItem down;

    /**
     * 左
     */
    private GoItem left;

    /**
     * 右
     */
    private GoItem right;

    /**
     * 所属游戏
     */
    private final GoGame game;

    public GoItem(int x, int y, GoGame game) {
        this.x = x;
        this.y = y;
        this.game = game;
    }

    public int getX() {
        return x;
    }

    public int getY() {
        return y;
    }

    public Color getColor() {
        return color;
    }

    public void setColor(Color color) {
        this.color = color;
        if (color == null) {
            setFill(Color.TRANSPARENT);
        } else {
            setFill(color);
        }
    }

    public GoItem getUp() {
        return up;
    }

    public void setUp(GoItem up) {
        this.up = up;
    }

    public GoItem getDown() {
        return down;
    }

    public void setDown(GoItem down) {
        this.down = down;
    }

    public GoItem getLeft() {
        return left;
    }

    public void setLeft(GoItem left) {
        this.left = left;
    }

    public GoItem getRight() {
        return right;
    }

    public void setRight(GoItem right) {
        this.right = right;
    }

    public GoGame getGame() {
        return game;
    }
}
