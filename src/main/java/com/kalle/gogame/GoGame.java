package com.kalle.gogame;

import javafx.application.Application;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.embed.swing.SwingFXUtils;
import javafx.geometry.Insets;
import javafx.geometry.Bounds;
import javafx.geometry.Point2D;
import javafx.scene.*;
import javafx.scene.control.*;
import javafx.scene.control.Alert.AlertType;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.ImageCursor;
import javafx.scene.image.WritableImage;
import javafx.scene.input.MouseButton;
import javafx.scene.input.MouseEvent;
import javafx.scene.input.ScrollEvent;
import javafx.scene.layout.*;
import javafx.scene.paint.Color;
import javafx.scene.paint.ImagePattern;
import javafx.scene.paint.LinearGradient;
import javafx.scene.paint.Paint;
import javafx.scene.paint.Stop;
import javafx.scene.paint.CycleMethod;
import javafx.scene.shape.*;
import java.util.ArrayList;
import java.util.List;
import javafx.scene.canvas.Canvas;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.Cursor;
import javafx.scene.text.Text;
import javafx.scene.transform.Scale;
import javafx.scene.transform.Translate;
import javafx.stage.FileChooser;
import javafx.stage.Stage;
import javafx.application.Platform;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 围棋游戏 - 支持高级矢量图形和画布操作
 *
 * <AUTHOR>
 * @since 2022-11-18
 */
public class GoGame extends Application {

    private void enableBgSlider(boolean enable) {
        if (bgScaleSlider != null) {
            bgScaleSlider.setDisable(!enable);
        }
    }

    private ComboBox<String> colorSelector;
    private ComboBox<String> skinSelector;
    private Slider bgScaleSlider;

    /**
     * 围棋路数
     */
    private int ROAD;
    // alias for compatibility with earlier code
    private int roadCount;

    /**
     * 边距
     */
    private static final int PADDING = 30;
    // 最大窗口尺寸，超出则通过缩放适配
    private static final int MAX_STAGE_W = 1400;
    private static final int MAX_STAGE_H = 900;

    /**
     * 容器左边距
     */
    private static final int X0 = 50;

    /**
     * 容器上边距
     */
    private static final int Y0 = 50;

    /**
     * 棋盘背景区域
     */
    // 背景矩形（非SVG时使用）
    private Rectangle boardBackground;
    // SVG背景相关
    private ImageView bgSvgView;
    private final Scale bgScale = new Scale(1, 1);

    /**
     * 容器:填充所有元素
     */
    private final Group CONTAINER = new Group();

    /**
     * 支持平移缩放的视图组
     */
    private final Group viewGroup = new Group();

    /**
     * 支持的棋子颜色列表
     */
    private static final List<Color> SUPPORTED_COLORS = Arrays.asList(
            Color.BLACK,    // 0: 黑色
            Color.WHITE,    // 1: 白色
            Color.RED,      // 2: 红色
            Color.GREEN,    // 3: 绿色
            Color.BLUE,     // 4: 蓝色
            Color.YELLOW,   // 5: 黄色
            Color.MAGENTA,  // 6: 洋红色
            Color.CYAN,     // 7: 青色
            Color.ORANGE,   // 8: 橙色
            Color.PURPLE,   // 9: 紫色
            Color.BROWN,    // 10: 棕色
            Color.GRAY      // 11: 灰色
    );

    /**
     * 颜色名称列表，用于下拉选择框
     */
    private static final List<String> COLOR_NAMES = Arrays.asList(
            "黑色", "白色", "红色", "绿色", "蓝色",
            "黄色", "洋红色", "青色", "橙色", "紫色", "棕色", "灰色"
    );

    /**
     * 棋盘棋子数组
     */
    // 棋盘交点棋子数组
private GoItem[][] goItems;
// 该交点的影响标记（一个半透明的小正方形）
private Rectangle[][] influenceMarks;
// 该交点显示手数的文本
private Text[][] moveTexts;

    /**
     * 当前选择的颜色索引
     */
    private int currentColorIndex = 0;

    /**
     * 当前步数
     */
    private final AtomicInteger globalCurrentStep = new AtomicInteger(0);

    /**
     * 行棋记录
     */
    private final Stack<GoRecord> doLog = new Stack<>();

    /**
     * 回退记录
     */
    private final Stack<GoRecord> undoLog = new Stack<>();

    /**
     * 当前指针，指在需要当前棋子上
     */
    private static final Polygon POINTER = new Polygon(0, 0, 11, 0, 0, 11);

    // ================= 绘图工具相关 =================
    private enum Tool {
        NONE,
        LINE,
        FILL,
        ICON,
        STONE
    }

    // 填充模式枚举
    private enum FillMode {
        SINGLE,     // 单格填充
        REGION,     // 区域填充（相同颜色的连通区域）
        SMART       // 智能填充（自动检测最佳填充方式）
    }

    private Tool currentTool = Tool.NONE;
    private FillMode currentFillMode = FillMode.SMART;
    private Canvas overlayCanvas; // for fill/icon
    private Group drawGroup; // store pen strokes
    private Path currentPath;
    private Polygon currentPolygon;
    private Cursor bucketCursor;
    private double polygonStartX;
    private double polygonStartY;
    private double pressX, pressY;
    private final Stack<Node> drawDoLog = new Stack<>();
    private final Stack<Node> drawUndoLog = new Stack<>();
    private ColorPicker drawColorPicker;
    private Image selectedIcon;
    private double prevDrawX, prevDrawY;

    // 铅笔画笔相关
    private boolean isDrawing = false;
    private Path currentPencilPath;
    private List<Point2D> currentStroke = new ArrayList<>();

    // 存储所有绘制的路径，用于填充时的闭合检测
    private List<List<Point2D>> allDrawnPaths = new ArrayList<>();

    // 绘制操作类，用于更好的撤销/重做管理
    private static class DrawOperation {
        Node node;
        List<Point2D> pathData; // 如果是路径操作，存储路径数据

        DrawOperation(Node node, List<Point2D> pathData) {
            this.node = node;
            this.pathData = pathData;
        }
    }

    private final Stack<DrawOperation> drawOperationLog = new Stack<>();
    private final Stack<DrawOperation> drawOperationUndoLog = new Stack<>();

    // 填充预览相关
    private Group fillPreviewGroup = new Group();
    private boolean showFillPreview = true;
    // =================================================

    /**
     * 主舞台
     */
    private Stage primaryStage;

    /**
     * 预设皮肤列表
     */
    private static final Map<String, String> PRESET_SKINS = new LinkedHashMap<>();
    static {
        PRESET_SKINS.put("木质默认", null); // 默认背景色
        PRESET_SKINS.put("木纹1", "wood1.jpg");
        PRESET_SKINS.put("木纹2", "wood2.jpg");
        PRESET_SKINS.put("石纹", "stone.jpg");
        PRESET_SKINS.put("竹子", "bamboo.jpg");
    }

    /**
     * 当前皮肤名称
     */
    private String currentSkin = "木质默认";

    /**
     * 平移变换
     */
    private final Translate translate = new Translate();

    /**
     * 缩放变换
     */
    private final Scale scale = new Scale(1, 1);

    /**
     * 鼠标按下位置
     */
    private Point2D mousePressed;

    /**
     * 是否正在拖拽
     */
    private boolean isDragging = false;

    /**
     * 画布最大缩放比例
     */
    private static final double MAX_SCALE = 50.0;

    /**
     * 允许下棋的最小可见格子数（单边）
     */
    private static final int MIN_VISIBLE_GRIDS = 50;

    /**
     * 画布最小缩放比例
     */
    private static final double MIN_SCALE = 0.1;

    @Override
    public void start(Stage primaryStage) {
        this.primaryStage = primaryStage;

        // 弹出对话框获取网格大小
        TextInputDialog dialog = new TextInputDialog("19");
        dialog.setTitle("输入网格大小");
        dialog.setHeaderText("请输入围棋棋盘的网格大小（例如：19）");
        dialog.setContentText("网格大小:");

        Optional<String> result = dialog.showAndWait();
        if (result.isPresent()) {
            try {
                ROAD = Integer.parseInt(result.get());
                roadCount = ROAD;
                if (ROAD < 1) {
                    throw new NumberFormatException();
                }
            } catch (NumberFormatException e) {
                Alert alert = new Alert(AlertType.ERROR);
                alert.setTitle("输入错误");
                alert.setHeaderText("无效的网格大小");
                alert.setContentText("请输入一个大于 0 的整数。");
                alert.showAndWait();
                System.exit(0);
            }
        } else {
            System.exit(0);
        }

        // 设置关闭事件
        primaryStage.setOnCloseRequest(e -> System.exit(0));

        // 根据棋盘大小调整场景大小，为控制面板预留240px宽度
        int sceneWidth = Math.min(MAX_STAGE_W, X0 + ROAD * PADDING + 240);
        int sceneHeight = Math.min(MAX_STAGE_H, Y0 + ROAD * PADDING + 150);
        Scene scene = new Scene(CONTAINER, sceneWidth, sceneHeight);
        scene.setFill(Color.rgb(216, 178, 133));
        primaryStage.setResizable(true);

        // 初始化视图变换
        initViewTransforms();

        // 初始化棋盘背景
        initBoardBackground();

        // 初始化绘图层
        initDrawingLayer();
        // 绘制棋盘
        this.drawGoTable();

        // 初始化棋盘
        this.initGoItems();

        // 增加操作控件
        this.addOperationControls();

        // 显示窗口
        primaryStage.setTitle("围棋游戏 v1.3 - 支持高级矢量图形");
        primaryStage.setScene(scene);
        scene.setCursor(Cursor.DEFAULT);
        primaryStage.show();
        // 确保大棋盘初始可见
        Platform.runLater(this::adjustInitialScale);
    }

    /**
     * 初始化视图变换（平移和缩放）
     */
    private void initViewTransforms() {
        // 设置缩放的pivot point为棋盘中心
        double boardCenterX = X0 + (ROAD - 1) * PADDING / 2.0;
        double boardCenterY = Y0 + (ROAD - 1) * PADDING / 2.0;
        scale.setPivotX(boardCenterX);
        scale.setPivotY(boardCenterY);

        // 同时设置SVG背景缩放的pivot point为棋盘中心
        bgScale.setPivotX(boardCenterX);
        bgScale.setPivotY(boardCenterY);

        // 当窗口大小变化时重新调整缩放，保证菜单可见
        primaryStage.widthProperty().addListener((obs, o, n) -> adjustInitialScale());
        primaryStage.heightProperty().addListener((obs, o, n) -> adjustInitialScale());
        viewGroup.getTransforms().addAll(translate, scale);
        CONTAINER.getChildren().add(viewGroup);

        // 添加鼠标事件处理
        CONTAINER.addEventHandler(MouseEvent.MOUSE_PRESSED, this::handleMousePressed);
        CONTAINER.addEventHandler(MouseEvent.MOUSE_RELEASED, this::handleMouseReleased);
        CONTAINER.addEventHandler(MouseEvent.MOUSE_DRAGGED, this::handleMouseDragged);
        CONTAINER.addEventHandler(ScrollEvent.SCROLL, this::handleScroll);
    }

    /**
     * 根据窗口大小自动缩放棋盘，使操作面板始终可见
     */
    private void adjustInitialScale() {
        if (ROAD <= 0) return;
        // 为ScrollPane控制面板预留固定宽度
        double controlsWidth = 240;
        double availableWidth = primaryStage.getWidth() - controlsWidth - 20;
        double availableHeight = primaryStage.getHeight() - 100;
        double boardWidth = (ROAD - 1) * PADDING + 2 * X0;
        double boardHeight = (ROAD - 1) * PADDING + 2 * Y0;
        double factor = Math.min(1.0, Math.min(availableWidth / boardWidth, availableHeight / boardHeight));
        scale.setX(factor);
        scale.setY(factor);
        updateGridStroke();

        // 计算棋盘中心点
        double boardCenterX = X0 + (ROAD - 1) * PADDING / 2.0;
        double boardCenterY = Y0 + (ROAD - 1) * PADDING / 2.0;

        // 计算可用区域的中心
        double viewCenterX = availableWidth / 2.0;
        double viewCenterY = availableHeight / 2.0 + 50; // 50px顶部偏移

        // 设置平移，使棋盘中心对齐到可视区域中心
        translate.setX(viewCenterX - boardCenterX * factor);
        translate.setY(viewCenterY - boardCenterY * factor);
    }

    /**
     * 根据当前缩放调整网格线粗细和星位大小
     */
    private void updateGridStroke() {
        double w = 1 / scale.getX();
        for (Node n : viewGroup.getChildren()) {
            if (n instanceof Line) {
                ((Line) n).setStrokeWidth(w);
            } else if (n instanceof Circle) {
                Circle c = (Circle) n;
                if (c.getRadius() < 6) { // 星位较小，按比例调整
                    c.setRadius(4 * w);
                }
            }
        }
    }

    /**
     * 处理鼠标按下事件
     */
    private void handleMousePressed(MouseEvent event) {
        if (event.getButton() == MouseButton.MIDDLE) {
            mousePressed = new Point2D(event.getSceneX(), event.getSceneY());
            // 标记正在拖拽
            isDragging = true;
        }
    }

    /**
     * 处理鼠标释放事件
     */
    private void handleMouseReleased(MouseEvent event) {
        if (event.getButton() == MouseButton.MIDDLE) {
            // 标记拖拽结束
            isDragging = false;
        }
    }

    /**
     * 处理鼠标拖拽事件（平移）
     */
    private void handleMouseDragged(MouseEvent event) {
        if (isDragging) {
            double deltaX = event.getSceneX() - mousePressed.getX();
            double deltaY = event.getSceneY() - mousePressed.getY();

            translate.setX(translate.getX() + deltaX);
            translate.setY(translate.getY() + deltaY);

            mousePressed = new Point2D(event.getSceneX(), event.getSceneY());
        }
    }

    /**
     * 处理滚动事件（缩放）
     */
    private void handleScroll(ScrollEvent event) {
        double zoomFactor = 1.05;
        double deltaY = event.getDeltaY();

        // 由于已经设置了Scale的pivot point为棋盘中心，
        // 缩放会自动以棋盘中心为基准进行，无需额外的平移调整
        if (deltaY < 0) {
            // 缩小
            scale.setX(Math.max(scale.getX() / zoomFactor, MIN_SCALE));
            scale.setY(Math.max(scale.getY() / zoomFactor, MIN_SCALE));
        } else {
            // 放大
            scale.setX(Math.min(scale.getX() * zoomFactor, MAX_SCALE));
            scale.setY(Math.min(scale.getY() * zoomFactor, MAX_SCALE));
        }
        updateGridStroke();
    }




    /**
     * 将场景坐标转换为棋盘坐标
     */
    private Point2D sceneToBoard(double sceneX, double sceneY) {
        double boardX = (sceneX - translate.getX()) / scale.getX() - X0;
        double boardY = (sceneY - translate.getY()) / scale.getY() - Y0;

        return new Point2D(boardX, boardY);
    }

    /**
     * 初始化棋盘背景
     */
    private void initDrawingLayer() {
        drawGroup = new Group();
        // 将绘图层(可缩放)添加到棋盘组
        viewGroup.getChildren().addAll(drawGroup, fillPreviewGroup);

        // 覆盖画布仅用于捕获鼠标事件，不应随棋盘缩放，否则在大棋盘缩小时无法覆盖全部区域
        overlayCanvas = new Canvas(primaryStage.getWidth(), primaryStage.getHeight());
        overlayCanvas.widthProperty().bind(primaryStage.widthProperty());
        overlayCanvas.heightProperty().bind(primaryStage.heightProperty());
        overlayCanvas.setMouseTransparent(true);

        overlayCanvas.addEventHandler(MouseEvent.MOUSE_PRESSED, this::handleDrawPressed);
        overlayCanvas.addEventHandler(MouseEvent.MOUSE_DRAGGED, this::handleDrawDragged);
        overlayCanvas.addEventHandler(MouseEvent.MOUSE_RELEASED, this::handleDrawReleased);
        overlayCanvas.addEventHandler(MouseEvent.MOUSE_MOVED, this::handleDrawMoved);

        // 画布应位于最上层
        CONTAINER.getChildren().add(overlayCanvas);
    }

    // 判断点是否在多边形内部（射线法）
    private boolean pointInPolygon(double x, double y, ObservableList<Double> pts){
        int n = pts.size()/2;
        boolean inside=false;
        for(int i=0,j=n-1;i<n;j=i++){
            double xi=pts.get(i*2), yi=pts.get(i*2+1);
            double xj=pts.get(j*2), yj=pts.get(j*2+1);
            boolean intersect = ((yi>y)!=(yj>y)) && (x < (xj-xi)*(y-yi)/(yj-yi)+xi);
            if(intersect) inside=!inside;
        }
        return inside;
    }

    /**
     * 将场景坐标转换为棋盘( viewGroup )内的本地坐标，确保在缩放/平移后依然准确。
     */
    private Point2D eventToBoard(MouseEvent e) {
        return viewGroup.sceneToLocal(e.getSceneX(), e.getSceneY());
    }

    private void handleDrawPressed(MouseEvent e) {
        Point2D boardPt = eventToBoard(e);
        pressX = boardPt.getX();
        pressY = boardPt.getY();

        if (currentTool == Tool.LINE) {
            // 开始铅笔绘制
            startPencilDrawing(pressX, pressY);
        } else if (currentTool == Tool.FILL) {
            // 执行填充操作
            performFloodFill(pressX, pressY, drawColorPicker.getValue());
        } else if (currentTool == Tool.ICON && selectedIcon != null) {
            ImageView iv = new ImageView(selectedIcon);
            // 缩放图标到半个格子大小
            double iconSize = PADDING / 2.0;
            iv.setFitWidth(iconSize);
            iv.setFitHeight(iconSize);
            iv.setPreserveRatio(true);
            Point2D boardPtIcon = eventToBoard(e);
            iv.setX(boardPtIcon.getX() - iconSize / 2);
            iv.setY(boardPtIcon.getY() - iconSize / 2);
            drawGroup.getChildren().add(iv);

            // 记录操作
            DrawOperation operation = new DrawOperation(iv, null);
            drawOperationLog.push(operation);
            drawOperationUndoLog.clear();

            drawDoLog.push(iv);
            drawUndoLog.clear();
        }
    }

    /**
     * 开始铅笔绘制
     */
    private void startPencilDrawing(double x, double y) {
        isDrawing = true;
        currentStroke = new ArrayList<>();
        currentStroke.add(new Point2D(x, y));

        // 创建新的路径
        currentPencilPath = new Path();
        currentPencilPath.setStroke(drawColorPicker.getValue());
        currentPencilPath.setStrokeWidth(2);
        currentPencilPath.setFill(Color.TRANSPARENT);
        currentPencilPath.setMouseTransparent(true);

        // 移动到起始点
        MoveTo moveTo = new MoveTo(x, y);
        currentPencilPath.getElements().add(moveTo);

        // 添加到绘图组
        drawGroup.getChildren().add(currentPencilPath);
    }

/**
 * 处理鼠标拖拽事件（绘制）
 */
private void handleDrawDragged(MouseEvent e) {
    if (currentTool == Tool.LINE && isDrawing && currentPencilPath != null) {
        Point2D boardPt = eventToBoard(e);
        double currentX = boardPt.getX();
        double currentY = boardPt.getY();

        // 检查距离，避免过多的点
        if (!currentStroke.isEmpty()) {
            Point2D lastPoint = currentStroke.get(currentStroke.size() - 1);
            double dx = currentX - lastPoint.getX();
            double dy = currentY - lastPoint.getY();
            double distance = Math.sqrt(dx * dx + dy * dy);

            // 只有当距离大于阈值时才添加新点
            if (distance < 3) {
                return;
            }
        }

        // 添加点到当前笔画
        currentStroke.add(new Point2D(currentX, currentY));

        // 使用平滑的曲线连接点
        if (currentStroke.size() >= 3) {
            addSmoothCurveToPath(currentX, currentY);
        } else {
            // 前两个点使用直线
            LineTo lineTo = new LineTo(currentX, currentY);
            currentPencilPath.getElements().add(lineTo);
        }
    }
}

/**
 * 添加平滑曲线到路径
 */
private void addSmoothCurveToPath(double currentX, double currentY) {
    int size = currentStroke.size();
    if (size < 3) return;

    Point2D p1 = currentStroke.get(size - 3);
    Point2D p2 = currentStroke.get(size - 2);
    Point2D p3 = currentStroke.get(size - 1);

    // 计算控制点，创建平滑的二次贝塞尔曲线
    double cp1X = p2.getX() + (p3.getX() - p1.getX()) * 0.1;
    double cp1Y = p2.getY() + (p3.getY() - p1.getY()) * 0.1;

    // 移除最后一个LineTo元素（如果存在）
    ObservableList<PathElement> elements = currentPencilPath.getElements();
    if (elements.size() > 1 && elements.get(elements.size() - 1) instanceof LineTo) {
        elements.remove(elements.size() - 1);
    }

    // 添加二次贝塞尔曲线
    QuadCurveTo curve = new QuadCurveTo(cp1X, cp1Y, currentX, currentY);
    currentPencilPath.getElements().add(curve);
}

/**
 * 处理鼠标释放事件（结束绘制）
 */
private void handleDrawReleased(MouseEvent e) {
    if (currentTool == Tool.LINE && isDrawing && currentPencilPath != null) {
        // 完成铅笔绘制
        finishPencilDrawing();
    }
}

/**
 * 完成铅笔绘制
 */
private void finishPencilDrawing() {
    if (currentPencilPath != null && !currentStroke.isEmpty()) {
        // 保存路径副本用于填充检测
        List<Point2D> pathCopy = new ArrayList<>(currentStroke);
        allDrawnPaths.add(pathCopy);

        // 检查路径是否闭合，如果接近闭合则给出视觉提示
        if (isPathClosed(pathCopy)) {
            // 可以在这里添加闭合提示，比如改变线条颜色或添加特效
            Paint currentStroke = currentPencilPath.getStroke();
            if (currentStroke instanceof Color) {
                currentPencilPath.setStroke(((Color) currentStroke).deriveColor(0, 1.2, 1, 1));
            }
        }

        // 创建绘制操作记录
        DrawOperation operation = new DrawOperation(currentPencilPath, pathCopy);
        drawOperationLog.push(operation);
        drawOperationUndoLog.clear();

        // 同时保持旧的日志系统兼容性
        drawDoLog.push(currentPencilPath);
        drawUndoLog.clear();

        // 重置绘制状态
        isDrawing = false;
        currentPencilPath = null;
        currentStroke.clear();
    }
}

/**
 * 检测路径是否闭合
 * @param stroke 笔画点列表
 * @return 是否闭合
 */
private boolean isPathClosed(List<Point2D> stroke) {
    if (stroke.size() < 4) return false; // 至少需要4个点才能形成闭合区域

    Point2D start = stroke.get(0);
    Point2D end = stroke.get(stroke.size() - 1);

    // 计算起点和终点的距离
    double dx = end.getX() - start.getX();
    double dy = end.getY() - start.getY();
    double distance = Math.sqrt(dx * dx + dy * dy);

    // 如果距离小于阈值，认为是闭合的
    double threshold = PADDING * 0.5; // 增加阈值，更容易检测到闭合
    return distance < threshold;
}

/**
 * 检测点是否在闭合路径内部
 * @param point 测试点
 * @param stroke 路径点列表
 * @return 是否在内部
 */
private boolean isPointInsideClosedPath(Point2D point, List<Point2D> stroke) {
    if (!isPathClosed(stroke)) return false;

    // 使用射线法判断点是否在多边形内部
    double x = point.getX();
    double y = point.getY();
    boolean inside = false;

    for (int i = 0, j = stroke.size() - 1; i < stroke.size(); j = i++) {
        Point2D pi = stroke.get(i);
        Point2D pj = stroke.get(j);

        if (((pi.getY() > y) != (pj.getY() > y)) &&
            (x < (pj.getX() - pi.getX()) * (y - pi.getY()) / (pj.getY() - pi.getY()) + pi.getX())) {
            inside = !inside;
        }
    }

    return inside;
}

    private void drawUndo() {
        if (!drawOperationLog.isEmpty()) {
            DrawOperation operation = drawOperationLog.pop();
            drawGroup.getChildren().remove(operation.node);
            drawOperationUndoLog.push(operation);

            // 如果撤销的是路径操作，从路径列表中移除
            if (operation.pathData != null) {
                allDrawnPaths.remove(operation.pathData);
            }
        }

        // 保持旧系统兼容性
        if (!drawDoLog.isEmpty()) {
            Node n = drawDoLog.pop();
            drawUndoLog.push(n);
        }
    }

    private void drawRedo() {
        if (!drawOperationUndoLog.isEmpty()) {
            DrawOperation operation = drawOperationUndoLog.pop();
            drawGroup.getChildren().add(operation.node);
            drawOperationLog.push(operation);

            // 如果重做的是路径操作，重新添加到路径列表
            if (operation.pathData != null) {
                allDrawnPaths.add(operation.pathData);
            }
        }

        // 保持旧系统兼容性
        if (!drawUndoLog.isEmpty()) {
            Node n = drawUndoLog.pop();
            drawGroup.getChildren().add(n);
            drawDoLog.push(n);
        }
    }

    /**
     * 处理鼠标移动事件（填充预览）
     */
    private void handleDrawMoved(MouseEvent e) {
        if (currentTool == Tool.FILL && showFillPreview) {
            updateFillPreview(e);
        }
    }

    /**
     * 更新填充预览
     */
    private void updateFillPreview(MouseEvent e) {
        // 清除之前的预览
        fillPreviewGroup.getChildren().clear();

        Point2D boardPt = eventToBoard(e);
        double previewX = boardPt.getX();
        double previewY = boardPt.getY();

        // 检查是否在棋盘范围内
        if (previewX < X0 || previewX > X0 + (ROAD - 1) * PADDING ||
            previewY < Y0 || previewY > Y0 + (ROAD - 1) * PADDING) {
            return;
        }

        // 找到最近的网格交点
        int gridX = (int) Math.round((previewX - X0) / PADDING);
        int gridY = (int) Math.round((previewY - Y0) / PADDING);

        // 确保在有效范围内
        gridX = Math.max(0, Math.min(ROAD - 1, gridX));
        gridY = Math.max(0, Math.min(ROAD - 1, gridY));

        // 创建预览形状
        Node previewShape = createFillPreview(gridX, gridY);
        if (previewShape != null) {
            fillPreviewGroup.getChildren().add(previewShape);
        }
    }

    /**
     * 创建填充预览形状
     */
    private Node createFillPreview(int gridX, int gridY) {
        Color previewColor = drawColorPicker.getValue().deriveColor(0, 1, 1, 0.3);
        double actualX = X0 + gridX * PADDING;
        double actualY = Y0 + gridY * PADDING;
        Point2D previewPoint = new Point2D(actualX, actualY);

        switch (currentFillMode) {
            case SINGLE:
                return createSingleCellPreview(gridX, gridY, previewColor);
            case REGION:
                return createRegionPreview(gridX, gridY, previewColor);
            case SMART:
            default:
                // 智能模式：优先检查是否在闭合路径内
                List<Point2D> enclosingPath = findBestEnclosingPath(previewPoint);
                if (enclosingPath != null) {
                    return createClosedAreaPreview(enclosingPath, previewColor);
                } else {
                    return createSinglePointPreview(actualX, actualY, previewColor);
                }
        }
    }

    /**
     * 创建单点预览
     */
    private Node createSinglePointPreview(double centerX, double centerY, Color previewColor) {
        double previewSize = PADDING * 0.3;

        Rectangle preview = new Rectangle(
            centerX - previewSize,
            centerY - previewSize,
            previewSize * 2,
            previewSize * 2
        );

        preview.setFill(previewColor);
        preview.setStroke(previewColor.deriveColor(0, 1, 0.7, 0.8));
        preview.setStrokeWidth(2);
        preview.setStrokeType(StrokeType.OUTSIDE);
        preview.getStrokeDashArray().addAll(5.0, 5.0);
        preview.setMouseTransparent(true);

        return preview;
    }

    /**
     * 创建单格预览
     */
    private Node createSingleCellPreview(int gridX, int gridY, Color previewColor) {
        double cellSize = PADDING * 0.95; // 和实际填充大小一致
        double actualX = X0 + gridX * PADDING;
        double actualY = Y0 + gridY * PADDING;

        Rectangle preview = new Rectangle(
            actualX - cellSize/2,
            actualY - cellSize/2,
            cellSize,
            cellSize
        );

        preview.setFill(previewColor);
        preview.setStroke(previewColor.deriveColor(0, 1, 0.7, 0.8));
        preview.setStrokeWidth(2);
        preview.setStrokeType(StrokeType.OUTSIDE);
        preview.getStrokeDashArray().addAll(5.0, 5.0);
        preview.setMouseTransparent(true);

        // 设置圆角，和实际填充一致
        preview.setArcWidth(cellSize * 0.1);
        preview.setArcHeight(cellSize * 0.1);

        return preview;
    }

    /**
     * 创建闭合区域预览
     */
    private Node createClosedAreaPreview(List<Point2D> enclosingPath, Color previewColor) {
        if (enclosingPath == null || enclosingPath.isEmpty()) {
            return null;
        }

        // 创建预览多边形
        Polygon previewPoly = new Polygon();
        for (Point2D point : enclosingPath) {
            previewPoly.getPoints().addAll(point.getX(), point.getY());
        }

        previewPoly.setFill(previewColor);
        previewPoly.setStroke(previewColor.deriveColor(0, 1, 0.7, 0.8));
        previewPoly.setStrokeWidth(2);
        previewPoly.getStrokeDashArray().addAll(5.0, 5.0);
        previewPoly.setMouseTransparent(true);

        return previewPoly;
    }

    /**
     * 创建单格预览
     */
    private Node createSingleCellPreview(int gridX, int gridY, Color previewColor) {
        double cellSize = PADDING * 0.8;
        double actualX = X0 + gridX * PADDING;
        double actualY = Y0 + gridY * PADDING;

        Rectangle preview = new Rectangle(
            actualX - cellSize/2,
            actualY - cellSize/2,
            cellSize,
            cellSize
        );

        preview.setFill(previewColor);
        preview.setStroke(previewColor.deriveColor(0, 1, 0.7, 0.8));
        preview.setStrokeWidth(2);
        preview.setStrokeType(StrokeType.OUTSIDE);
        preview.getStrokeDashArray().addAll(5.0, 5.0); // 虚线边框
        preview.setMouseTransparent(true);

        return preview;
    }

    /**
     * 创建区域预览
     */
    private Node createRegionPreview(int startX, int startY, Color previewColor) {
        if (goItems == null || goItems[startY][startX].getColor() == null) {
            return createSingleCellPreview(startX, startY, previewColor);
        }

        Color targetColor = goItems[startY][startX].getColor();
        boolean[][] visited = new boolean[ROAD][ROAD];
        List<Point2D> previewPoints = new ArrayList<>();
        floodFillSearch(startX, startY, targetColor, visited, previewPoints);

        if (previewPoints.isEmpty()) {
            return null;
        }

        // 创建预览组合
        Group previewGroup = new Group();
        double cellSize = PADDING * 0.7;

        for (Point2D point : previewPoints) {
            double actualX = X0 + point.getX() * PADDING;
            double actualY = Y0 + point.getY() * PADDING;

            Rectangle cell = new Rectangle(
                actualX - cellSize/2,
                actualY - cellSize/2,
                cellSize,
                cellSize
            );
            cell.setFill(previewColor);
            cell.setStroke(previewColor.deriveColor(0, 1, 0.7, 0.8));
            cell.setStrokeWidth(1);
            cell.getStrokeDashArray().addAll(3.0, 3.0);
            cell.setMouseTransparent(true);

            previewGroup.getChildren().add(cell);
        }

        return previewGroup;
    }

    /**
     * 执行洪水填充算法
     * @param startX 起始X坐标（棋盘坐标系）
     * @param startY 起始Y坐标（棋盘坐标系）
     * @param fillColor 填充颜色
     */
    private void performFloodFill(double startX, double startY, Color fillColor) {
        // 创建填充区域的多边形
        Polygon fillArea = createFillArea(startX, startY, fillColor);
        if (fillArea != null) {
            drawGroup.getChildren().add(fillArea);
            drawDoLog.push(fillArea);
        }
        // 清空重做记录
        drawUndoLog.clear();
    }

    /**
     * 创建填充区域
     * 主要用于填充铅笔画出的闭合区域
     */
    private Polygon createFillArea(double centerX, double centerY, Color fillColor) {
        // 检查点击位置是否在棋盘范围内
        if (centerX < X0 || centerX > X0 + (ROAD - 1) * PADDING ||
            centerY < Y0 || centerY > Y0 + (ROAD - 1) * PADDING) {
            return null;
        }

        Point2D clickPoint = new Point2D(centerX, centerY);

        // 根据填充模式决定填充行为
        switch (currentFillMode) {
            case SINGLE:
                // 单格填充模式：填充一个格子大小的区域
                int gridX = (int) Math.round((centerX - X0) / PADDING);
                int gridY = (int) Math.round((centerY - Y0) / PADDING);
                gridX = Math.max(0, Math.min(ROAD - 1, gridX));
                gridY = Math.max(0, Math.min(ROAD - 1, gridY));
                return createSingleCellFill(gridX, gridY, fillColor);
            case REGION:
                // 区域填充模式：填充棋子的连通区域
                gridX = (int) Math.round((centerX - X0) / PADDING);
                gridY = (int) Math.round((centerY - Y0) / PADDING);
                gridX = Math.max(0, Math.min(ROAD - 1, gridX));
                gridY = Math.max(0, Math.min(ROAD - 1, gridY));
                return createRegionFill(gridX, gridY, fillColor);
            case SMART:
            default:
                // 智能模式：优先检查是否在铅笔画的闭合区域内
                List<Point2D> enclosingPath = findBestEnclosingPath(clickPoint);
                if (enclosingPath != null) {
                    return createClosedAreaFill(enclosingPath, fillColor);
                }
                // 如果不在闭合区域内，则进行单点填充
                else {
                    return createSinglePointFill(centerX, centerY, fillColor);
                }
        }
    }

    /**
     * 创建单点填充（在指定位置创建一个小的填充区域）
     */
    private Polygon createSinglePointFill(double centerX, double centerY, Color fillColor) {
        double fillSize = PADDING * 0.3; // 填充区域大小

        Polygon fillPoly = new Polygon();
        fillPoly.getPoints().addAll(
            centerX - fillSize, centerY - fillSize,  // 左上
            centerX + fillSize, centerY - fillSize,  // 右上
            centerX + fillSize, centerY + fillSize,  // 右下
            centerX - fillSize, centerY + fillSize   // 左下
        );

        fillPoly.setFill(fillColor.deriveColor(0, 1, 1, 0.8));
        fillPoly.setStroke(fillColor);
        fillPoly.setStrokeWidth(1);
        fillPoly.setMouseTransparent(true);

        // 直接添加到绘图组
        drawGroup.getChildren().add(fillPoly);

        // 记录操作
        DrawOperation operation = new DrawOperation(fillPoly, null);
        drawOperationLog.push(operation);
        drawOperationUndoLog.clear();

        drawDoLog.push(fillPoly);

        return null; // 已经直接添加了
    }

    /**
     * 查找包含指定点的闭合路径
     */
    private List<Point2D> findEnclosingClosedPath(Point2D point) {
        // 按路径创建时间倒序检查，优先检查最新的路径
        for (int i = allDrawnPaths.size() - 1; i >= 0; i--) {
            List<Point2D> path = allDrawnPaths.get(i);
            if (isPointInsideClosedPath(point, path)) {
                return path;
            }
        }
        return null;
    }

    /**
     * 查找最适合的闭合路径（包括近似闭合的路径）
     */
    private List<Point2D> findBestEnclosingPath(Point2D point) {
        List<Point2D> bestPath = null;
        double bestDistance = Double.MAX_VALUE;

        for (List<Point2D> path : allDrawnPaths) {
            if (path.size() < 4) continue; // 至少需要4个点

            // 检查是否闭合或接近闭合
            Point2D start = path.get(0);
            Point2D end = path.get(path.size() - 1);
            double dx = end.getX() - start.getX();
            double dy = end.getY() - start.getY();
            double distance = Math.sqrt(dx * dx + dy * dy);

            // 如果路径接近闭合且包含该点
            if (distance < PADDING && isPointInsideClosedPath(point, path)) {
                if (distance < bestDistance) {
                    bestDistance = distance;
                    bestPath = path;
                }
            }
        }

        return bestPath;
    }

    /**
     * 创建闭合区域填充
     */
    private Polygon createClosedAreaFill(List<Point2D> enclosingPath, Color fillColor) {
        if (enclosingPath == null || enclosingPath.isEmpty()) {
            return null;
        }

        // 如果路径不闭合，先闭合它
        List<Point2D> closedPath = new ArrayList<>(enclosingPath);
        if (!isPathClosed(closedPath)) {
            // 添加起始点来闭合路径
            closedPath.add(closedPath.get(0));
        }

        // 创建填充多边形
        Polygon fillPoly = new Polygon();
        for (Point2D point : closedPath) {
            fillPoly.getPoints().addAll(point.getX(), point.getY());
        }

        // 设置填充样式 - 使用半透明填充，不遮挡底层内容
        fillPoly.setFill(fillColor.deriveColor(0, 1, 1, 0.4));
        fillPoly.setStroke(Color.TRANSPARENT); // 不显示边框，避免与原有线条重复
        fillPoly.setMouseTransparent(true);

        // 将填充层放在绘制层的底部，这样线条会显示在填充之上
        drawGroup.getChildren().add(0, fillPoly);

        // 记录操作
        DrawOperation operation = new DrawOperation(fillPoly, null);
        drawOperationLog.push(operation);
        drawOperationUndoLog.clear();

        drawDoLog.push(fillPoly);

        return null; // 已经直接添加了
    }

    /**
     * 创建单格填充
     */
    private Polygon createSingleCellFill(int gridX, int gridY, Color fillColor) {
        double cellSize = PADDING * 0.95; // 和格子一样大，稍微小一点避免重叠
        double actualX = X0 + gridX * PADDING;
        double actualY = Y0 + gridY * PADDING;

        // 创建圆角矩形而不是多边形，看起来更美观
        Rectangle fillRect = new Rectangle(
            actualX - cellSize/2,
            actualY - cellSize/2,
            cellSize,
            cellSize
        );

        // 设置圆角
        fillRect.setArcWidth(cellSize * 0.1);
        fillRect.setArcHeight(cellSize * 0.1);

        // 创建渐变填充效果
        Stop[] stops = new Stop[] {
            new Stop(0, fillColor.deriveColor(0, 1, 1.2, 0.8)),
            new Stop(1, fillColor.deriveColor(0, 1, 0.8, 0.6))
        };
        LinearGradient gradient = new LinearGradient(0, 0, 1, 1, true, CycleMethod.NO_CYCLE, stops);
        fillRect.setFill(gradient);

        fillRect.setStroke(fillColor.deriveColor(0, 1, 0.7, 0.9));
        fillRect.setStrokeWidth(1);
        fillRect.setMouseTransparent(true);

        // 添加到绘图组并返回null（因为已经直接添加了）
        drawGroup.getChildren().add(fillRect);
        drawDoLog.push(fillRect);

        return null;
    }

    /**
     * 创建区域填充（填充相同颜色的连通区域）
     */
    private Polygon createRegionFill(int startX, int startY, Color fillColor) {
        Color targetColor = goItems[startY][startX].getColor();
        if (targetColor == null) {
            return createSingleCellFill(startX, startY, fillColor);
        }

        // 使用洪水填充算法找到所有相同颜色的连通区域
        boolean[][] visited = new boolean[ROAD][ROAD];
        List<Point2D> fillPoints = new ArrayList<>();
        floodFillSearch(startX, startY, targetColor, visited, fillPoints);

        if (fillPoints.isEmpty()) {
            return null;
        }

        // 创建包含所有填充点的复合形状
        return createCompoundFillShape(fillPoints, fillColor);
    }

    /**
     * 洪水填充搜索算法
     */
    private void floodFillSearch(int x, int y, Color targetColor, boolean[][] visited, List<Point2D> fillPoints) {
        if (x < 0 || x >= ROAD || y < 0 || y >= ROAD || visited[y][x]) {
            return;
        }

        Color currentColor = goItems[y][x].getColor();
        if (currentColor == null || !colorsEqual(currentColor, targetColor)) {
            return;
        }

        visited[y][x] = true;
        fillPoints.add(new Point2D(x, y));

        // 递归搜索四个方向
        floodFillSearch(x + 1, y, targetColor, visited, fillPoints);
        floodFillSearch(x - 1, y, targetColor, visited, fillPoints);
        floodFillSearch(x, y + 1, targetColor, visited, fillPoints);
        floodFillSearch(x, y - 1, targetColor, visited, fillPoints);
    }

    /**
     * 比较两个颜色是否相等
     */
    private boolean colorsEqual(Color c1, Color c2) {
        if (c1 == null && c2 == null) return true;
        if (c1 == null || c2 == null) return false;
        return Math.abs(c1.getRed() - c2.getRed()) < 0.01 &&
               Math.abs(c1.getGreen() - c2.getGreen()) < 0.01 &&
               Math.abs(c1.getBlue() - c2.getBlue()) < 0.01;
    }

    /**
     * 创建复合填充形状
     */
    private Polygon createCompoundFillShape(List<Point2D> fillPoints, Color fillColor) {
        if (fillPoints.isEmpty()) return null;

        // 创建一个包含所有点的多边形
        Polygon compound = new Polygon();

        // 简化版：为每个点创建一个小正方形，然后合并
        double cellSize = PADDING * 0.7;

        for (Point2D point : fillPoints) {
            double actualX = X0 + point.getX() * PADDING;
            double actualY = Y0 + point.getY() * PADDING;

            // 创建单个格子的填充
            Rectangle cell = new Rectangle(
                actualX - cellSize/2,
                actualY - cellSize/2,
                cellSize,
                cellSize
            );
            cell.setFill(fillColor.deriveColor(0, 1, 1, 0.5));
            cell.setStroke(fillColor);
            cell.setStrokeWidth(0.5);
            cell.setMouseTransparent(true);

            drawGroup.getChildren().add(cell);
            drawDoLog.push(cell);
        }

        return null; // 已经直接添加到drawGroup了
    }

    /**
     * 切换绘图/落子工具，并更新光标和事件透传
     */
    private void activateTool(Tool tool) {
        currentTool = tool;
        Scene sc = primaryStage.getScene();
        if (sc == null) return;
        switch (tool) {
            case LINE:
                sc.setCursor(Cursor.CROSSHAIR);
                overlayCanvas.setMouseTransparent(false);
                break;
            case FILL:
                // 懒加载颜料桶光标
                if (bucketCursor == null) {
                    try {
                        Image bucketImg = new Image(getClass().getResourceAsStream("/icons/bucket.png"));
                        bucketCursor = new ImageCursor(bucketImg, bucketImg.getWidth() / 2, bucketImg.getHeight() / 2);
                    } catch (Exception ex) {
                        bucketCursor = Cursor.DEFAULT;
                    }
                }
                sc.setCursor(bucketCursor);
                overlayCanvas.setMouseTransparent(false);
                break;
            case ICON:
                sc.setCursor(Cursor.DEFAULT);
                overlayCanvas.setMouseTransparent(false);
                break;
            case STONE:
                sc.setCursor(Cursor.DEFAULT);
                overlayCanvas.setMouseTransparent(true);
                break;
            default:
                sc.setCursor(Cursor.DEFAULT);
                overlayCanvas.setMouseTransparent(true);
                break;
        }
    }




    private void initBoardBackground() {
        int boardWidth = ROAD * PADDING;
        int boardHeight = ROAD * PADDING;

        boardBackground = new Rectangle(X0, Y0, boardWidth, boardHeight);
        setBoardSkin(currentSkin);

        viewGroup.getChildren().add(0, boardBackground); // 添加到最底层
    }

    /**
     * 设置棋盘皮肤
     */
    private void setBoardSkin(String skinName) {
        currentSkin = skinName;

        // 确保boardBackground已初始化
        if (boardBackground == null) {
            return;
        }

        if ("木质默认".equals(skinName)) {
            // 使用默认背景色
            boardBackground.setFill(Color.rgb(216, 178, 133));
        } else if (PRESET_SKINS.containsKey(skinName)) {
            // 使用预设皮肤
            String resourceName = PRESET_SKINS.get(skinName);
            try {
                Image image = new Image(getClass().getResourceAsStream("/skins/" + resourceName));
                boardBackground.setFill(new ImagePattern(image));
            } catch (Exception e) {
                boardBackground.setFill(Color.rgb(216, 178, 133));
                Toast.toast("无法加载皮肤: " + skinName);
            }
        } else if (skinName.startsWith("custom:")) {
            // 使用自定义皮肤
            String filePath = skinName.substring(7);
            File file = new File(filePath);
            useCustomSkin(file);
        }
    }

    /**
     * 使用自定义皮肤
     */
    private void useCustomSkin(File file) {
        try {
            // 确保boardBackground已初始化
            if (boardBackground == null) {
                Toast.toast("棋盘背景未初始化，无法加载皮肤");
                return;
            }

            // 检查文件大小
            long fileSize = file.length();
            if (fileSize > 10 * 1024 * 1024) { // 10MB以上的文件提示用户
                Alert alert = new Alert(AlertType.WARNING);
                alert.setTitle("大文件提示");
                alert.setHeaderText("正在加载大尺寸文件");
                alert.setContentText("您选择的文件较大，可能需要一些时间加载。请耐心等待。");
                alert.showAndWait();
            }

            // 对于SVG文件，使用专门的SVG加载器
            if (file.getName().toLowerCase().endsWith(".svg")) {
                loadSvgBackground(file);
            } else {
                // 其他格式作为普通图片加载
                Image image = new Image(file.toURI().toString(),
                        ROAD * PADDING, ROAD * PADDING, true, true);
                boardBackground.setFill(new ImagePattern(image));
            }

            String customSkinName = "custom:" + file.getAbsolutePath();
            currentSkin = customSkinName;

            // 更新皮肤选择器（如果已初始化）
            if (skinSelector != null) {
                if (!skinSelector.getItems().contains("自定义皮肤")) {
                    skinSelector.getItems().add("自定义皮肤");
                }
                skinSelector.setValue("自定义皮肤");
            }
            // 如果是SVG已加载，则启用滑块
            if (file.getName().toLowerCase().endsWith(".svg")) {
                enableBgSlider(true);
            }

            Toast.toast("已加载皮肤: " + file.getName());
        } catch (Exception e) {
            if (boardBackground != null) {
                boardBackground.setFill(Color.rgb(216, 178, 133));
            }
            Toast.toast("加载皮肤出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 加载SVG背景图
     */
    private void loadSvgBackground(File file) {
        try {
            // 确保必要的组件已初始化
            if (viewGroup == null || boardBackground == null) {
                Toast.toast("界面组件未初始化，无法加载SVG背景");
                return;
            }

            BufferedImage svgImage = SvgUtils.loadSvg(file, ROAD * PADDING, ROAD * PADDING);
            javafx.scene.image.Image fxImage = SwingFXUtils.toFXImage(svgImage, null);
            // 如果之前已有 svgView 先移除
            if (bgSvgView != null) {
                viewGroup.getChildren().remove(bgSvgView);
            }
            bgSvgView = new ImageView(fxImage);
            bgSvgView.setPreserveRatio(true);
            bgSvgView.setX(X0);
            bgSvgView.setY(Y0);

            // 设置SVG背景缩放的中心点为棋盘中心
            double boardCenterX = X0 + (ROAD - 1) * PADDING / 2.0;
            double boardCenterY = Y0 + (ROAD - 1) * PADDING / 2.0;
            bgScale.setPivotX(boardCenterX);
            bgScale.setPivotY(boardCenterY);

            bgSvgView.getTransforms().add(bgScale);
            viewGroup.getChildren().add(0, bgSvgView); // 最底层
            // 透明化矩形背景，避免双层
            boardBackground.setFill(Color.TRANSPARENT);
            enableBgSlider(true);
        } catch (Exception e) {
            if (boardBackground != null) {
                boardBackground.setFill(Color.rgb(216, 178, 133));
            }
            Toast.toast("加载SVG失败: " + e.getMessage());
            e.printStackTrace();
            enableBgSlider(false);
        }
    }

    /**
     * 添加操作控件
     */
    private void addOperationControls() {
        // 创建一个垂直布局容器
        VBox controls = new VBox(8);
        controls.setPrefWidth(200);
        controls.setMaxWidth(200);
        controls.setPadding(new Insets(10));

        // 背景缩放 Slider
        Label bgScaleLbl = new Label("背景缩放:");
        Slider bgSlider = new Slider(0.1, 2.0, 1.0);
        bgSlider.setPrefWidth(180);
        bgSlider.setMaxWidth(180);
        bgSlider.valueProperty().addListener((obs, o, v) -> {
            bgScale.setX(v.doubleValue());
            bgScale.setY(v.doubleValue());
        });
        // 初始禁用，只有加载SVG后启用
        bgSlider.setDisable(true);
        this.bgScaleSlider = bgSlider;

        // 颜色选择器
        Label colorLabel = new Label("选择棋子颜色:");
        colorSelector = new ComboBox<>(FXCollections.observableArrayList(COLOR_NAMES));
        colorSelector.setValue(COLOR_NAMES.get(currentColorIndex));
        colorSelector.setPrefWidth(180);
        colorSelector.setMaxWidth(180);

        colorSelector.setOnAction(e -> {
            currentColorIndex = COLOR_NAMES.indexOf(colorSelector.getValue());
        });

        // 皮肤选择器
        Label skinLabel = new Label("选择棋盘皮肤:");
        skinSelector = new ComboBox<>(FXCollections.observableArrayList(PRESET_SKINS.keySet()));
        this.skinSelector = skinSelector;
        skinSelector.setValue(currentSkin);
        skinSelector.setPrefWidth(180);
        skinSelector.setMaxWidth(180);

        skinSelector.setOnAction(e -> {
            String selectedSkin = skinSelector.getValue();
            setBoardSkin(selectedSkin);
        });

        // 自定义皮肤按钮
        Button customSkinButton = new Button("自定义皮肤");
        customSkinButton.setPrefWidth(180);
        customSkinButton.setMaxWidth(180);
        customSkinButton.setOnAction(e -> {
            FileChooser fileChooser = new FileChooser();
            fileChooser.setTitle("选择棋盘皮肤图片");
            fileChooser.getExtensionFilters().addAll(
                    new FileChooser.ExtensionFilter("所有支持的图片", "*.jpg", "*.jpeg", "*.png", "*.svg"),
                    new FileChooser.ExtensionFilter("JPG图片", "*.jpg", "*.jpeg"),
                    new FileChooser.ExtensionFilter("PNG图片", "*.png"),
                    new FileChooser.ExtensionFilter("SVG图片", "*.svg")
            );

            File selectedFile = fileChooser.showOpenDialog(primaryStage);
            if (selectedFile != null) {
                useCustomSkin(selectedFile);
            }
        });

        // 画布操作按钮
        Label canvasLabel = new Label("画布操作:");
        HBox canvasButtons = new HBox(10);

        Button resetViewButton = new Button("重置视图");
        resetViewButton.setPrefWidth(85);
        resetViewButton.setMaxWidth(85);
        resetViewButton.setOnAction(e -> resetView());

        Button exportButton = new Button("导出");
        exportButton.setPrefWidth(85);
        exportButton.setMaxWidth(85);
        exportButton.setOnAction(e -> exportBoard());

        canvasButtons.getChildren().addAll(resetViewButton, exportButton);

        // ===== 工具面板 =====
        Label toolLabel = new Label("工具选择:");
        ToggleGroup toolGroup = new ToggleGroup();

        // 下棋工具（默认选中）
        RadioButton stoneBtn = new RadioButton("下棋");
        stoneBtn.setToggleGroup(toolGroup);
        stoneBtn.setSelected(true);
        stoneBtn.setPrefWidth(85);
        stoneBtn.setMaxWidth(85);

        RadioButton penBtn = new RadioButton("铅笔");
        penBtn.setToggleGroup(toolGroup);
        penBtn.setPrefWidth(85);
        penBtn.setMaxWidth(85);

        RadioButton fillBtn = new RadioButton("填充");
        fillBtn.setToggleGroup(toolGroup);
        fillBtn.setPrefWidth(85);
        fillBtn.setMaxWidth(85);

        RadioButton iconBtn = new RadioButton("图标");
        iconBtn.setToggleGroup(toolGroup);
        iconBtn.setPrefWidth(85);
        iconBtn.setMaxWidth(85);

        drawColorPicker = new ColorPicker(Color.RED);
        drawColorPicker.setPrefWidth(180);
        drawColorPicker.setMaxWidth(180);

        // 填充模式选择器
        Label fillModeLabel = new Label("填充模式:");
        ComboBox<String> fillModeSelector = new ComboBox<>();
        fillModeSelector.getItems().addAll("智能填充", "单格填充", "区域填充");
        fillModeSelector.setValue("智能填充");
        fillModeSelector.setPrefWidth(180);
        fillModeSelector.setMaxWidth(180);
        fillModeSelector.setOnAction(e -> {
            String selected = fillModeSelector.getValue();
            switch (selected) {
                case "单格填充":
                    currentFillMode = FillMode.SINGLE;
                    break;
                case "区域填充":
                    currentFillMode = FillMode.REGION;
                    break;
                default:
                    currentFillMode = FillMode.SMART;
                    break;
            }
        });

        // 填充预览开关
        CheckBox fillPreviewCheckBox = new CheckBox("显示填充预览");
        fillPreviewCheckBox.setSelected(true);
        fillPreviewCheckBox.setOnAction(e -> {
            showFillPreview = fillPreviewCheckBox.isSelected();
            if (!showFillPreview) {
                fillPreviewGroup.getChildren().clear();
            }
        });

        Button loadIconBtn = new Button("加载图标");
        loadIconBtn.setPrefWidth(180);
        loadIconBtn.setMaxWidth(180);
        loadIconBtn.setOnAction(ev -> {
            FileChooser fc = new FileChooser();
            fc.setTitle("选择图标图片");
            File f = fc.showOpenDialog(primaryStage);
            if (f != null) {
                selectedIcon = new Image(f.toURI().toString());
            }
        });

        toolGroup.selectedToggleProperty().addListener((obs,o,n)->{
            if (n == stoneBtn) {
                activateTool(Tool.STONE);
            } else if (n == penBtn) {
                activateTool(Tool.LINE);
            } else if (n == fillBtn) {
                activateTool(Tool.FILL);
            } else if (n == iconBtn) {
                activateTool(Tool.ICON);
            }
        });

        Button drawUndoBtn=new Button("撤销");
        drawUndoBtn.setPrefWidth(85);
        drawUndoBtn.setMaxWidth(85);
        drawUndoBtn.setOnAction(ev->drawUndo());

        Button drawRedoBtn=new Button("重做");
        drawRedoBtn.setPrefWidth(85);
        drawRedoBtn.setMaxWidth(85);
        drawRedoBtn.setOnAction(ev->drawRedo());

        // 默认激活下棋工具
        activateTool(Tool.STONE);

        // 工具按钮行1
        HBox toolRow1 = new HBox(3, stoneBtn, penBtn);
        toolRow1.setPrefWidth(180);
        toolRow1.setMaxWidth(180);

        // 工具按钮行2
        HBox toolRow2 = new HBox(3, fillBtn, iconBtn);
        toolRow2.setPrefWidth(180);
        toolRow2.setMaxWidth(180);

        // 撤销重做按钮行
        HBox undoRedoRow = new HBox(5, drawUndoBtn, drawRedoBtn);
        undoRedoRow.setPrefWidth(180);
        undoRedoRow.setMaxWidth(180);

        VBox toolBox=new VBox(5,
            toolLabel,
            toolRow1,
            toolRow2,
            new Label("绘图颜色:"), drawColorPicker,
            fillModeLabel, fillModeSelector,
            fillPreviewCheckBox,
            loadIconBtn,
            undoRedoRow
        );
        controls.getChildren().addAll(new Separator(), toolBox);

        // 画布操作说明
        Label canvasInstructions = new Label("操作说明:\n" +
                "- 按住鼠标中键拖动平移\n" +
                "- 滚动鼠标滚轮缩放\n" +
                "- 缩放中心为鼠标位置");
        canvasInstructions.setWrapText(true);
        canvasInstructions.setStyle("-fx-font-size: 11px;");

        // 游戏操作按钮
        Label gameLabel = new Label("游戏操作:");
        Button restartButton = new Button("重新开始");
        restartButton.setPrefSize(180, 28);
        restartButton.setMaxWidth(180);
        restartButton.setOnMouseClicked(e -> reset());

        Button backwardButton = new Button("后退");
        backwardButton.setPrefSize(180, 28);
        backwardButton.setMaxWidth(180);
        backwardButton.setOnMouseClicked(e -> backward());

        Button forwardButton = new Button("前进");
        forwardButton.setPrefSize(180, 28);
        forwardButton.setMaxWidth(180);
        forwardButton.setOnMouseClicked(e -> forward());

        // 保存和加载按钮
        Button saveButton = new Button("保存游戏");
        saveButton.setPrefSize(180, 28);
        saveButton.setMaxWidth(180);
        saveButton.setOnMouseClicked(e -> saveGame());

        Button loadButton = new Button("加载游戏");
        loadButton.setPrefSize(180, 28);
        loadButton.setMaxWidth(180);
        loadButton.setOnMouseClicked(e -> loadGame());

        // 添加到容器中
        controls.getChildren().addAll(
                bgScaleLbl, bgSlider,
                new Separator(),
                colorLabel, colorSelector,
                new Separator(),
                skinLabel, skinSelector, customSkinButton,
                new Separator(),
                canvasLabel, canvasButtons,
                canvasInstructions,
                new Separator(),
                gameLabel, restartButton, backwardButton, forwardButton,
                new Separator(),
                saveButton, loadButton
        );

        // 使用ScrollPane包装控制面板，防止内容超出窗口
        ScrollPane controlsScrollPane = new ScrollPane(controls);
        controlsScrollPane.setFitToWidth(true);
        controlsScrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        controlsScrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        controlsScrollPane.setPrefWidth(220);
        controlsScrollPane.setMaxWidth(220);
        controlsScrollPane.setStyle("-fx-background: transparent; -fx-background-color: transparent;");

        // 设置ScrollPane的位置
        controlsScrollPane.layoutXProperty().bind(
            primaryStage.widthProperty().subtract(controlsScrollPane.getPrefWidth()).subtract(10)
        );
        controlsScrollPane.setLayoutY(50);
        controlsScrollPane.prefHeightProperty().bind(primaryStage.heightProperty().subtract(60));

        CONTAINER.getChildren().add(controlsScrollPane);
    }

    /**
     * 重置视图（平移和缩放）
     */
    private void resetView() {
        // 重置缩放比例为1.0
        scale.setX(1.0);
        scale.setY(1.0);
        updateGridStroke();

        // 计算棋盘中心点
        double boardCenterX = X0 + (ROAD - 1) * PADDING / 2.0;
        double boardCenterY = Y0 + (ROAD - 1) * PADDING / 2.0;

        // 计算可用区域的中心（排除控制面板）
        double availableWidth = primaryStage.getWidth() - 240; // 为控制面板预留240px
        double availableHeight = primaryStage.getHeight() - 100; // 为顶部和底部预留空间
        double viewCenterX = availableWidth / 2.0;
        double viewCenterY = availableHeight / 2.0 + 50; // 50px顶部偏移

        // 设置平移，使棋盘中心对齐到可视区域中心
        translate.setX(viewCenterX - boardCenterX);
        translate.setY(viewCenterY - boardCenterY);
    }

    /**
     * 导出棋盘为图片或SVG
     */
    private void exportBoard() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("导出棋盘");
        fileChooser.getExtensionFilters().addAll(
                new FileChooser.ExtensionFilter("所有支持的格式", "*.jpg", "*.svg"),
                new FileChooser.ExtensionFilter("JPG图片", "*.jpg"),
                new FileChooser.ExtensionFilter("SVG矢量图", "*.svg")
        );

        File selectedFile = fileChooser.showSaveDialog(primaryStage);
        if (selectedFile != null) {
            String filePath = selectedFile.getAbsolutePath();
            try {
                if (filePath.toLowerCase().endsWith(".jpg")) {
                    exportAsJPG(selectedFile);
                } else if (filePath.toLowerCase().endsWith(".svg")) {
                    exportAsSVG(selectedFile);
                } else {
                    Toast.toast("不支持的文件格式，请选择.jpg或.svg");
                }
            } catch (Exception e) {
                Toast.toast("导出失败: " + e.getMessage());
            }
        }
    }

    /**
     * 导出为JPG图片
     */
    private void exportAsJPG(File file) throws IOException {
        int width = (int) (X0 + ROAD * PADDING);
        int height = (int) (Y0 + ROAD * PADDING);

        WritableImage image = new WritableImage(width, height);
        SnapshotParameters params = new SnapshotParameters();
        params.setFill(Color.TRANSPARENT);

        // 创建一个临时组，不包含控制UI
        Group tempGroup = new Group();
        for (Node node : viewGroup.getChildren()) {
            tempGroup.getChildren().add(node);
        }

        tempGroup.snapshot(params, image);
        BufferedImage bufferedImage = SwingFXUtils.fromFXImage(image, null);
        ImageIO.write(bufferedImage, "jpg", file);

        Toast.toast("已导出为JPG图片: " + file.getAbsolutePath());
    }

    /**
     * 导出为SVG矢量图
     */
    private void exportAsSVG(File file) throws IOException {
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(file))) {
            int width = (int) (X0 + ROAD * PADDING);
            int height = (int) (Y0 + ROAD * PADDING);

            writer.write("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n");
            writer.write("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"" + width + "\" height=\"" + height + "\">\n");

            // 写入背景
            writer.write("  <rect x=\"" + X0 + "\" y=\"" + Y0 + "\" width=\"" + (ROAD * PADDING) + "\" height=\"" + (ROAD * PADDING) + "\" fill=\"rgb(216,178,133)\"/>\n");

            // 写入棋盘线条
            for (int i = 0; i < ROAD; i++) {
                // 横线
                writer.write("  <line x1=\"" + X0 + "\" y1=\"" + (Y0 + i * PADDING) + "\" x2=\"" + (X0 + (ROAD-1) * PADDING) + "\" y2=\"" + (Y0 + i * PADDING) + "\" stroke=\"black\" stroke-width=\"1\"/>\n");
                // 竖线
                writer.write("  <line x1=\"" + (X0 + i * PADDING) + "\" y1=\"" + Y0 + "\" x2=\"" + (X0 + i * PADDING) + "\" y2=\"" + (Y0 + (ROAD-1) * PADDING) + "\" stroke=\"black\" stroke-width=\"1\"/>\n");
            }

            // 写入天元和星位
            if (ROAD >= 9) {
                for (int i = 0; i < 3; i++) {
                    for (int j = 0; j < 3; j++) {
                        int cx = X0 + 3 * PADDING + j * 6 * PADDING;
                        int cy = Y0 + 3 * PADDING + i * 6 * PADDING;
                        writer.write("  <circle cx=\"" + cx + "\" cy=\"" + cy + "\" r=\"4\" fill=\"black\"/>\n");
                    }
                }
            }

            // 写入棋子
            for (int y = 0; y < ROAD; y++) {
                for (int x = 0; x < ROAD; x++) {
                    GoItem item = goItems[y][x];
                    if (item.getColor() != null) {
                        Color color = item.getColor();
                        int cx = X0 + x * PADDING;
                        int cy = Y0 + y * PADDING;
                        writer.write("  <circle cx=\"" + cx + "\" cy=\"" + cy + "\" r=\"13\" fill=\"rgb(" +
                                (int)(color.getRed() * 255) + "," +
                                (int)(color.getGreen() * 255) + "," +
                                (int)(color.getBlue() * 255) + ")\"/>\n");
                    }
                }
            }

            writer.write("</svg>");
        }
    }

    /**
     * 初始化棋盘交点、影响标记和手数文本，并绑定鼠标事件
     */
    private void initGoItems() {
        // 初始化数组
        goItems = new GoItem[ROAD][ROAD];
        influenceMarks = new Rectangle[ROAD][ROAD];
        moveTexts = new Text[ROAD][ROAD];
        for (int y = 0; y < ROAD; y++) {
            for (int x = 0; x < ROAD; x++) {
                GoItem current = new GoItem(x, y, this);
                goItems[y][x] = current;

                current.setRadius(13);
                current.setCenterX(X0 + x * PADDING);
                current.setCenterY(Y0 + y * PADDING);
                current.setFill(Color.TRANSPARENT);

                Rectangle mark = new Rectangle(10, 10);
                mark.setFill(Color.TRANSPARENT);
                mark.setTranslateX(current.getCenterX() - 5);
                mark.setTranslateY(current.getCenterY() - 5);
                mark.setMouseTransparent(true);
                influenceMarks[y][x] = mark;

                Text moveTxt = new Text("");
                moveTxt.setStyle("-fx-font-size: 10px; -fx-font-weight: bold;");
                moveTxt.setTranslateX(current.getCenterX() - 4);
                moveTxt.setTranslateY(current.getCenterY() + 4);
                moveTxt.setVisible(false);
                moveTxt.setMouseTransparent(true);
                moveTexts[y][x] = moveTxt;

                if (y > 0) {
                    current.setUp(goItems[y - 1][x]);
                    goItems[y - 1][x].setDown(current);
                }
                if (x > 0) {
                    current.setLeft(goItems[y][x - 1]);
                    goItems[y][x - 1].setRight(current);
                }

                current.setOnMouseClicked(e -> handleBoardClick(e, current));
                viewGroup.getChildren().addAll(mark, current, moveTxt);
            }
        }
    }

    /**
     * 分离出的棋盘点击逻辑
     */
    private void handleBoardClick(MouseEvent e, GoItem clickedItem) {
        // 若处于绘图模式，忽略棋盘点击
        if (currentTool != Tool.STONE) return;
        // 双击左键移除
        if (e.getButton() == MouseButton.PRIMARY && e.getClickCount() == 2) {
            if (clickedItem.getColor() != null) {
                clickedItem.setColor(null);
                moveTexts[clickedItem.getY()][clickedItem.getX()].setVisible(false);
            }
            return;
        }
        // 右键设置手数
        if (e.getButton() == MouseButton.SECONDARY) {
            if (clickedItem.getColor() != null) {
                TextInputDialog td = new TextInputDialog("" + globalCurrentStep.get());
                td.setTitle("设置手数");
                td.setHeaderText("请输入该棋子的手数");
                td.showAndWait().ifPresent(v -> {
                    Text mt = moveTexts[clickedItem.getY()][clickedItem.getX()];
                    mt.setText(v);
                    mt.setFill(getContrastColor(clickedItem.getColor()));
                    mt.setVisible(true);
                });
            }
            return;
        }
        // 单击左键落子
        if (e.getButton() == MouseButton.PRIMARY && e.getClickCount() == 1) {
            if (!canPlaceStone()) {
                showZoomRestrictionAlert();
                return;
            }
            put(clickedItem);
        }
    }

    /**
     * 落子监听事件
     *
     * @param current current
     */
    private void put(GoItem current) {
        // 落子音效
        WavPlayer.play();

        if (current.getColor() == null) {
            // 获取当前选择的颜色
            Color selectedColor = SUPPORTED_COLORS.get(currentColorIndex);

            // 设置棋子颜色
            current.setColor(selectedColor);

            // 每成功走一步，那么就将当前结果压入历史栈
            GoRecord record = new GoRecord(selectedColor, current, Collections.emptySet());
            doLog.push(record);

            // 走一步棋，清空undoLog
            undoLog.clear();

            // 更新当前步数
            globalCurrentStep.incrementAndGet();

            // 更新指针位置
//            updatePointer(current);

            // 显示手数
            Text moveTxt = moveTexts[current.getY()][current.getX()];
            moveTxt.setText(String.valueOf(globalCurrentStep.get()));
            moveTxt.setFill(getContrastColor(selectedColor));
            moveTxt.setVisible(true);

            // 影响周围交点
            int[][] dirs = {{1, 0}, {-1, 0}, {0, 1}, {0, -1}};
            for (int[] d : dirs) {
                int nx = current.getX() + d[0];
                int ny = current.getY() + d[1];
                if (nx < 0 || nx >= ROAD || ny < 0 || ny >= ROAD) continue;
                GoItem neighbor = goItems[ny][nx];
                Rectangle mark = influenceMarks[ny][nx];
                if (neighbor.getColor() == null && ((Color) mark.getFill()).getOpacity() == 0) {
                    Color c = selectedColor.deriveColor(0, 1, 1, 0.3);
                    mark.setFill(c);
                }
            }
        }
    }

    /**
     * 绘制棋盘
     */
    private void drawGoTable() {
        // 画横竖线
        int maxDest = (ROAD - 1) * PADDING;

        for (int i = 0; i < ROAD; i++) {
            // 横线
            Line lineH = new Line();
            lineH.setStartX(X0);
            lineH.setStartY(Y0 + i * PADDING);
            lineH.setEndX(X0 + maxDest);
            lineH.setEndY(Y0 + i * PADDING);

            // 竖线
            Line lineV = new Line();
            lineV.setStartX(X0 + i * PADDING);
            lineV.setStartY(Y0);
            lineV.setEndX(X0 + i * PADDING);
            lineV.setEndY(Y0 + maxDest);

            viewGroup.getChildren().add(lineH);
            viewGroup.getChildren().add(lineV);
        }

        // 围棋上的小圆点
        if (ROAD >= 9) {
            for (int i = 0; i < 3; i++) {
                for (int j = 0; j < 3; j++) {
                    Circle circle = new Circle();
                    circle.setRadius(4);
                    circle.setCenterX(X0 + 3 * PADDING + j * 6 * PADDING);
                    circle.setCenterY(Y0 + 3 * PADDING + i * 6 * PADDING);
                    viewGroup.getChildren().add(circle);
                }
            }
        }
    }

    /**
     * 针对回退记录，前进一步
     */
    public void forward() {
        // 落子音效
        WavPlayer.play();

        if (undoLog.isEmpty()) {
            Toast.toast("没有历史记录");
            return;
        }

        // 当前步数加一
        globalCurrentStep.incrementAndGet();

        // 将回退记录出栈
        GoRecord pop = undoLog.pop();

        // 前进一步
        pop.getKiller().setColor(pop.getColor());

        // 推入操作记录中
        doLog.push(pop);

        // 更新指针位置
        updatePointer(pop.getKiller());
    }

    /**
     * 针对操作记录，回退一步
     */
    public void backward() {
        // 落子音效
        WavPlayer.play();

        if (doLog.isEmpty()) {
            Toast.toast("没有历史记录");
            return;
        }

        // 当前步数减一
        globalCurrentStep.decrementAndGet();

        // 将操作记录出栈
        GoRecord pop = doLog.pop();

        // 还原操作
        pop.getKiller().setColor(null);

        // 推入回退记录中
        undoLog.push(pop);

        if (doLog.isEmpty()) {
            // 隐藏指针
            hidePointer();
        } else {
            // 更新指针位置
            updatePointer(doLog.peek().getKiller());
        }
    }

    /**
     * 更新指针位置
     *
     * @param current current
     */
    public void updatePointer(GoItem current) {
        // 指针颜色使用与当前棋子对比明显的颜色
        Color pointerColor = getContrastColor(current.getColor());

        // 计算指针位置，考虑缩放和平移
        double pointerX = X0 + current.getX() * PADDING;
        double pointerY = Y0 + current.getY() * PADDING;

        POINTER.setTranslateX(pointerX);
        POINTER.setTranslateY(pointerY);
        POINTER.setFill(pointerColor);

        showPointer();
    }

    /**
     * 获取与给定颜色形成对比的颜色
     *
     * @param color 输入颜色
     * @return 对比颜色
     */
    private Color getContrastColor(Color color) {
        // 计算颜色的亮度
        double luminance = 0.299 * color.getRed() + 0.587 * color.getGreen() + 0.114 * color.getBlue();

        // 如果颜色较亮，返回黑色；否则返回白色
        return luminance > 0.5 ? Color.BLACK : Color.WHITE;
    }

    /**
     * 显示指针
     */
    public void showPointer() {
        // 移除指针
        CONTAINER.getChildren().remove(POINTER);
        POINTER.setOpacity(1);

        // 添加指针
        CONTAINER.getChildren().add(POINTER);
    }

    /**
     * 隐藏指针
     */
    public void hidePointer() {
        // 移除指针
        CONTAINER.getChildren().remove(POINTER);
        POINTER.setOpacity(0);
    }

    /**
     * 重置棋局
     */
    public void reset() {
        globalCurrentStep.set(0);

        // 隐藏指针
        hidePointer();

        // 消除棋盘上棋子的颜色和手数文本
        for (int y = 0; y < ROAD; y++) {
            for (int x = 0; x < ROAD; x++) {
                GoItem goItem = goItems[y][x];
                if (goItem.getColor() != null) {
                    goItem.setColor(null);
                }
                // 隐藏手数文本
                moveTexts[y][x].setVisible(false);
                // 清除影响标记
                influenceMarks[y][x].setFill(Color.TRANSPARENT);
            }
        }

        // 清空记录
        doLog.clear();
        undoLog.clear();

        // 清空绘图内容
        drawGroup.getChildren().clear();
        drawDoLog.clear();
        drawUndoLog.clear();

        // 清空新的绘制系统数据
        drawOperationLog.clear();
        drawOperationUndoLog.clear();
        allDrawnPaths.clear();
        fillPreviewGroup.getChildren().clear();

        // 重置绘制状态
        isDrawing = false;
        currentPencilPath = null;
        currentStroke.clear();

        // 重置视图
        resetView();
    }

    /**
     * 检查当前缩放级别是否允许下棋
     */
    private boolean canPlaceStone() {
        // 计算当前可见区域的格子数量
        double visibleWidth = primaryStage.getScene().getWidth() / scale.getX();
        double visibleHeight = primaryStage.getScene().getHeight() / scale.getY();

        int visibleGridsX = (int) Math.ceil(visibleWidth / PADDING);
        int visibleGridsY = (int) Math.ceil(visibleHeight / PADDING);

        // 当可见格子数小于阈值时允许下棋
        return visibleGridsX < MIN_VISIBLE_GRIDS || visibleGridsY < MIN_VISIBLE_GRIDS;
    }

    /**
     * 显示缩放限制提示
     */
    private void showZoomRestrictionAlert() {
        // nothing change
        Alert alert = new Alert(AlertType.INFORMATION);
        alert.setTitle("缩放限制");
        alert.setHeaderText("无法落子");
        alert.setContentText("请放大视图至显示区域小于50×50格后再尝试。");
        alert.showAndWait();
    }

    /**
     * 保存游戏状态
     */
    private void saveGame() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("保存游戏");
        fileChooser.getExtensionFilters().add(
                new FileChooser.ExtensionFilter("游戏存档文件", "*.gsg")
        );
        fileChooser.setInitialFileName("game.gsg");

        File selectedFile = fileChooser.showSaveDialog(primaryStage);
        if (selectedFile != null) {
            try {
                SaveData saveData = new SaveData();
                saveData.road = ROAD;
                saveData.currentStep = globalCurrentStep.get();

                // 保存棋子状态
                for (int y = 0; y < ROAD; y++) {
                    for (int x = 0; x < ROAD; x++) {
                        GoItem item = goItems[y][x];
                        if (item.getColor() != null) {
                            Text moveText = moveTexts[y][x];
                            String moveStr = moveText.isVisible() ? moveText.getText() : "";
                            saveData.stones.add(new SaveData.Stone(x, y, item.getColor(), moveStr));
                        }
                    }
                }

                // 保存绘图内容（多边形）
                for (Node node : drawGroup.getChildren()) {
                    if (node instanceof Polygon) {
                        Polygon poly = (Polygon) node;
                        SaveData.Poly polyData = new SaveData.Poly();
                        polyData.pts = new ArrayList<>(poly.getPoints());
                        polyData.stroke = SaveData.colorToHex((Color) poly.getStroke());
                        polyData.fill = SaveData.colorToHex((Color) poly.getFill());
                        saveData.polys.add(polyData);
                    }
                }

                // 序列化保存
                try (ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(selectedFile))) {
                    oos.writeObject(saveData);
                }

                Toast.toast("游戏已保存: " + selectedFile.getName());
            } catch (Exception e) {
                Toast.toast("保存失败: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    /**
     * 加载游戏状态
     */
    private void loadGame() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("加载游戏");
        fileChooser.getExtensionFilters().add(
                new FileChooser.ExtensionFilter("游戏存档文件", "*.gsg")
        );

        File selectedFile = fileChooser.showOpenDialog(primaryStage);
        if (selectedFile != null) {
            try {
                SaveData saveData;
                try (ObjectInputStream ois = new ObjectInputStream(new FileInputStream(selectedFile))) {
                    saveData = (SaveData) ois.readObject();
                }

                // 检查棋盘大小是否匹配
                if (saveData.road != ROAD) {
                    Alert alert = new Alert(AlertType.WARNING);
                    alert.setTitle("棋盘大小不匹配");
                    alert.setHeaderText("存档文件的棋盘大小与当前不匹配");
                    alert.setContentText("存档: " + saveData.road + "×" + saveData.road +
                                       "\n当前: " + ROAD + "×" + ROAD +
                                       "\n\n是否继续加载？（可能导致显示异常）");

                    ButtonType result = alert.showAndWait().orElse(ButtonType.CANCEL);
                    if (result != ButtonType.OK) {
                        return;
                    }
                }

                // 清空当前状态
                reset();

                // 恢复棋子状态
                for (SaveData.Stone stone : saveData.stones) {
                    if (stone.x >= 0 && stone.x < ROAD && stone.y >= 0 && stone.y < ROAD) {
                        GoItem item = goItems[stone.y][stone.x];
                        Color color = SaveData.hexToColor(stone.colorHex);
                        item.setColor(color);

                        // 恢复手数显示
                        Text moveTxt = moveTexts[stone.y][stone.x];
                        if (stone.moveText != null && !stone.moveText.isEmpty()) {
                            moveTxt.setText(stone.moveText);
                            moveTxt.setFill(getContrastColor(color));
                            moveTxt.setVisible(true);
                        }
                    }
                }

                // 更新全局步数
                globalCurrentStep.set(saveData.currentStep);

                // 恢复绘图内容
                drawGroup.getChildren().clear();
                for (SaveData.Poly polyData : saveData.polys) {
                    Polygon poly = new Polygon();
                    poly.getPoints().addAll(polyData.pts);
                    poly.setStroke(SaveData.hexToColor(polyData.stroke));
                    poly.setFill(SaveData.hexToColor(polyData.fill));
                    poly.setStrokeWidth(2);
                    poly.setMouseTransparent(true);
                    drawGroup.getChildren().add(poly);
                }

                Toast.toast("游戏已加载: " + selectedFile.getName());
            } catch (Exception e) {
                Toast.toast("加载失败: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }



        public static void main (String[]args){
            launch(args);
        }








/**
 * SVG处理工具类
 */
}

class SvgUtils {
    public static BufferedImage loadSvg(File file, int width, int height) {
        try {
            // 使用Batik渲染SVG
            java.net.URI uri = file.toURI();
            org.apache.batik.transcoder.TranscoderInput input = new org.apache.batik.transcoder.TranscoderInput(uri.toString());

            // 创建PNG转码器
            org.apache.batik.transcoder.image.PNGTranscoder t = new org.apache.batik.transcoder.image.PNGTranscoder();

            // 设置转码器的尺寸
            t.addTranscodingHint(org.apache.batik.transcoder.image.PNGTranscoder.KEY_WIDTH, (float) width);
            t.addTranscodingHint(org.apache.batik.transcoder.image.PNGTranscoder.KEY_HEIGHT, (float) height);

            // 创建输出流
            java.io.ByteArrayOutputStream ostream = new java.io.ByteArrayOutputStream();
            org.apache.batik.transcoder.TranscoderOutput output = new org.apache.batik.transcoder.TranscoderOutput(ostream);

            // 执行转码
            t.transcode(input, output);

            // 将输出流转换为BufferedImage
            ostream.flush();
            ostream.close();
            java.io.ByteArrayInputStream istream = new java.io.ByteArrayInputStream(ostream.toByteArray());
            return ImageIO.read(istream);
        } catch (Exception e) {
            throw new RuntimeException("SVG渲染失败: " + e.getMessage(), e);
        }
    }
}


