<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="EasyCodeTableSetting">
    <option name="tableInfoMap">
      <map>
        <entry key="hmdp.tb_blog">
          <value>
            <TableInfoDTO>
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="comment" value="主键" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="id" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="商户id" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="shopId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="用户id" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="userId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="标题" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="title" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="探店的照片，最多9张，多张以&quot;,&quot;隔开" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="images" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="探店的文字描述" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="content" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="点赞数量" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="liked" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="评论数量" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="comments" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="创建时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="createTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="更新时间" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="updateTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="TbBlog" />
              <option name="preName" value="" />
              <option name="saveModelName" value="" />
              <option name="savePackageName" value="" />
              <option name="savePath" value="" />
              <option name="templateGroupName" value="" />
            </TableInfoDTO>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>